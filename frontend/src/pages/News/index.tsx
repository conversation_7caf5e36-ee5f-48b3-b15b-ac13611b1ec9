import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Typography, Tag, Spin, Alert, message, Input, Select, Button, Image, Pagination } from 'antd';
import {
  FileTextOutlined,
  CalendarOutlined,
  SearchOutlined,
  FilterOutlined,
  LoadingOutlined,
  EyeOutlined
} from '@ant-design/icons';

import type { News } from '../../types';
import NewsService from '../../api/news';

const { Title, Paragraph, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const News: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [news, setNews] = useState<News[]>([]);
  const [filteredNews, setFilteredNews] = useState<News[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12; // 每页显示12条新闻

  // 获取新闻数据
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        setError(null);

        // 并行获取新闻数据和筛选选项
        const [newsData, typesData] = await Promise.all([
          NewsService.getAllNews({
            pageSize: 1000, // 获取所有新闻
            sort: 'date:desc,createdAt:desc'
          }),
          NewsService.getAvailableTypes()
        ]);

        setNews(newsData);
        setFilteredNews(newsData);
        setAvailableTypes(typesData);

      } catch (error) {
        console.error('获取新闻数据失败:', error);
        setError('获取新闻数据失败，请稍后重试');
        message.error('获取新闻数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, []);

  // 筛选和搜索逻辑
  useEffect(() => {
    let filtered = [...news];

    // 按类型筛选
    if (selectedType) {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(keyword) ||
        item.abs.toLowerCase().includes(keyword) ||
        item.type.toLowerCase().includes(keyword)
      );
    }

    setFilteredNews(filtered);
    setCurrentPage(1); // 重置到第一页
  }, [news, selectedType, searchKeyword]);

  // 获取新闻封面图URL
  const getNewsImageUrl = (newsItem: News): string | undefined => {
    if (!newsItem.news_pic) {
      return undefined;
    }

    const image = newsItem.news_pic;
    // 优先使用中等尺寸图片，适合列表展示
    const url = image.formats?.medium?.url || image.formats?.small?.url || image.url;

    // 如果是相对路径，添加API基础URL
    if (url && url.startsWith('/')) {
      return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
    }

    return url;
  };

  // 格式化日期显示
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // 获取类型标签颜色
  const getTypeColor = (type: string): string => {
    const colors = ['blue', 'green', 'orange', 'purple', 'red', 'cyan', 'magenta', 'gold'];
    const index = type.length % colors.length;
    return colors[index];
  };

  // 渲染单个新闻卡片
  const renderNewsCard = (newsItem: News) => {
    const imageUrl = getNewsImageUrl(newsItem);

    return (
      <Col xs={24} sm={12} lg={8} xl={6} key={newsItem.id}>
        <Card
          hoverable
          style={{
            height: '100%',
            borderRadius: '16px',
            overflow: 'hidden',
            border: '1px solid var(--border-secondary)',
            background: 'var(--bg-card)',
            transition: 'all 0.3s ease'
          }}
          styles={{ body: { padding: '20px' } }}
          cover={
            imageUrl ? (
              <div style={{
                height: '200px',
                overflow: 'hidden',
                background: 'var(--bg-tertiary)'
              }}>
                <Image
                  src={imageUrl}
                  alt={newsItem.title}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                  preview={false}
                />
              </div>
            ) : (
              <div style={{
                height: '200px',
                background: 'var(--gradient-secondary)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '16px 16px 0 0'
              }}>
                <Text style={{
                  fontSize: '24px',
                  fontWeight: '600',
                  color: 'var(--text-tertiary)'
                }}>
                  动态
                </Text>
              </div>
            )
          }
          actions={[
            <Button
              key="view"
              type="primary"
              icon={<EyeOutlined />}
              style={{
                borderRadius: '8px',
                background: 'var(--color-primary)',
                borderColor: 'var(--color-primary)'
              }}
              onClick={() => {
                navigate(`/news/${newsItem.id}`);
              }}
            >
              查看详情
            </Button>
          ]}
        >
          {/* 新闻类型标签 */}
          <div style={{ marginBottom: '12px' }}>
            <Tag
              color={getTypeColor(newsItem.type)}
              style={{
                borderRadius: '12px',
                padding: '4px 12px',
                fontSize: '12px',
                fontWeight: '500',
                border: 'none'
              }}
            >
              {newsItem.type}
            </Tag>
          </div>

          {/* 新闻标题 */}
          <Title
            level={5}
            style={{
              margin: '0 0 12px 0',
              color: 'var(--text-primary)',
              fontSize: '16px',
              fontWeight: '600',
              lineHeight: '1.4',
              height: '44px',
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
            title={newsItem.title}
          >
            {newsItem.title}
          </Title>

          {/* 新闻摘要 */}
          <Paragraph
            style={{
              color: 'var(--text-secondary)',
              fontSize: '14px',
              lineHeight: '1.5',
              margin: '0 0 16px 0',
              height: '63px',
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical'
            }}
            title={newsItem.abs}
          >
            {newsItem.abs}
          </Paragraph>

          {/* 新闻日期 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            color: 'var(--text-tertiary)',
            fontSize: '12px'
          }}>
            <CalendarOutlined style={{ marginRight: '6px' }} />
            <span>{formatDate(newsItem.date)}</span>
          </div>
        </Card>
      </Col>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <Spin
          size="large"
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{
            borderRadius: '12px',
            border: '1px solid var(--border-secondary)'
          }}
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '48px' }}>
        <Title level={1} style={{
          margin: 0,
          color: 'var(--text-primary)',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em',
          transition: 'color 0.3s ease'
        }}>
          新闻动态
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0',
          transition: 'color 0.3s ease'
        }}>
          了解课题组的最新动态、学术成果、项目进展和重要活动
        </Paragraph>
      </div>

      {/* 搜索和筛选区域 */}
      <div style={{
        background: 'var(--bg-card)',
        padding: '24px',
        borderRadius: '16px',
        border: '1px solid var(--border-secondary)',
        marginBottom: '32px',
        transition: 'all 0.3s ease'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={12}>
            <Search
              placeholder="搜索新闻标题或摘要..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onSearch={setSearchKeyword}
              style={{
                borderRadius: '12px'
              }}
            />
          </Col>
          <Col xs={24} md={8}>
            <Select
              placeholder="选择新闻类型"
              allowClear
              size="large"
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: '100%' }}
              suffixIcon={<FilterOutlined />}
            >
              {availableTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} md={4}>
            <div style={{
              color: 'var(--text-secondary)',
              fontSize: '14px',
              textAlign: 'right'
            }}>
              共 {filteredNews.length} 条新闻
            </div>
          </Col>
        </Row>
      </div>

      {/* 新闻列表 */}
      <div style={{ width: '100%' }}>
        {filteredNews.length > 0 ? (
          <div className="apple-fade-in">
            <Row gutter={[24, 24]}>
              {filteredNews
                .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                .map(renderNewsCard)}
            </Row>

            {/* 分页组件 */}
            {filteredNews.length > pageSize && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '48px'
              }}>
                <Pagination
                  current={currentPage}
                  total={filteredNews.length}
                  pageSize={pageSize}
                  onChange={(page) => {
                    setCurrentPage(page);
                    // 滚动到页面顶部
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 项，共 ${total} 条新闻`
                  }
                  className="news-pagination"
                />
              </div>
            )}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '80px 20px',
            color: 'var(--text-secondary)'
          }}>
            <FileTextOutlined style={{ fontSize: '64px', marginBottom: '16px' }} />
            <Title level={3} style={{ color: 'var(--text-secondary)', margin: '0 0 8px 0' }}>
              暂无新闻动态
            </Title>
            <Paragraph style={{ color: 'var(--text-tertiary)', margin: 0 }}>
              {searchKeyword || selectedType ? '没有找到符合条件的新闻' : '还没有发布任何新闻动态'}
            </Paragraph>
          </div>
        )}
      </div>
    </div>
  );
};

export default News; 