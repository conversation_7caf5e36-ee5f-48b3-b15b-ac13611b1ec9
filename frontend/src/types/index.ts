// 主题类型定义
export type ThemeMode = 'light' | 'dark';

export interface ThemeColors {
  // 基础颜色
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;

  // 背景颜色
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    elevated: string;
    card: string;
  };

  // 文字颜色
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    disabled: string;
  };

  // 边框颜色
  border: {
    primary: string;
    secondary: string;
    tertiary: string;
  };

  // 阴影
  shadow: {
    sm: string;
    md: string;
    lg: string;
  };

  // 渐变
  gradient: {
    primary: string;
    secondary: string;
    hero: string;
  };
}

export interface Theme {
  mode: ThemeMode;
  colors: ThemeColors;
}

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
}

// Strapi Blocks 类型定义
export interface StrapiBlockChild {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strikethrough?: boolean;
  code?: boolean;
}

export interface StrapiBlock {
  type: 'paragraph' | 'heading' | 'list' | 'quote' | 'code' | 'image' | 'link';
  level?: number; // for heading
  format?: 'ordered' | 'unordered'; // for list
  children?: StrapiBlockChild[];
}

export type StrapiBlocks = StrapiBlock[];

// API响应类型
export interface ApiResponse<T = unknown> {
  data: T;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: {
    id: number;
    name: string;
    description: string;
  };
  createdAt: string;
  updatedAt: string;
}

// 课题组成员类型 - 根据 Strapi v5 Content Type 更新
export interface TeamMember {
  id: number;
  documentId?: string; // Strapi v5 新增字段
  name: string;
  title: string;
  role: 'Mentor' | 'Collaborator' | 'RA' | 'Alumni' | 'PhD' | 'Master' | 'Bachelor';
  avatar?: {
    id: number;
    documentId?: string;
    name: string;
    alternativeText?: string;
    url: string;
    width?: number;
    height?: number;
    formats?: {
      thumbnail?: { url: string; width: number; height: number; };
      small?: { url: string; width: number; height: number; };
      medium?: { url: string; width: number; height: number; };
      large?: { url: string; width: number; height: number; };
    };
  }[];
  researchDirection: StrapiBlocks;
  email: string;
  phone?: string;
  website?: string;
  bio?: StrapiBlocks; // Strapi blocks 类型
  education?: StrapiBlocks; // Strapi blocks 类型
  enrollmentYear?: number; // 入学年份（学生）
  graduationYear?: number; // 毕业年份（校友）
  company?: string; // 当前公司（校友）
  position?: string; // 当前职位（校友）
  sortOrder?: number; // 排序权重
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Strapi v5 API 响应格式 - 扁平化结构，无 attributes 包装
export interface StrapiTeamMemberResponse {
  data: TeamMember[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// 新闻类型
export interface News {
  id: number;
  title: string;
  content: string;
  summary?: string;
  cover?: {
    url: string;
  };
  category: string;
  tags?: string[];
  publishedAt: string;
  createdAt: string;
  updatedAt: string;
}

// 科研项目类型 - 根据API接口规范重新定义
export interface Project {
  id: number;
  documentId?: string; // Strapi v5 新增字段
  funding_agency: string; // 资助机构 (必填)
  project_title: string; // 项目标题 (必填)
  funding_year?: number; // 资助年份 (可选)
  funding_type?: string; // 资助类型 (可选)
  project_abs?: StrapiBlocks; // 项目摘要 (可选，富文本)
  project_pic?: {
    id: number;
    documentId?: string;
    name: string;
    alternativeText?: string;
    url: string;
    width?: number;
    height?: number;
    formats?: {
      thumbnail?: { url: string; width: number; height: number; };
      small?: { url: string; width: number; height: number; };
      medium?: { url: string; width: number; height: number; };
      large?: { url: string; width: number; height: number; };
    };
  }; // 项目图片 (可选，单个对象)
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Strapi v5 API 响应格式 - 项目信息
export interface StrapiProjectResponse {
  data: Project[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// 学术成果类型
export interface AcademicAchievement {
  id: number;
  title: string;
  type: '论文' | '专利' | '获奖' | '报告';
  authors: string[];
  journal?: string;
  publicationDate: string;
  doi?: string;
  impactFactor?: number;
  citations?: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 研究方向类型
export interface ResearchDirection {
  id: number;
  title: string;
  description: string;
  keywords: string[];
  image?: {
    url: string;
  };
  createdAt: string;
  updatedAt: string;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sort?: string;
  filters?: Record<string, any>;
}

// 登录表单类型
export interface LoginForm {
  identifier: string;
  password: string;
}

// 注册表单类型
export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 论文信息类型 - 对应 paper-info API
export interface PaperInfo {
  id: number;
  documentId?: string; // Strapi v5 新增字段
  doi: string; // 必填字段
  title?: string;
  authors?: string;
  year?: number;
  journal?: string;
  abstract?: string;
  bibtex?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Strapi v5 API 响应格式 - 论文信息
export interface StrapiPaperInfoResponse {
  data: PaperInfo[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// 联系信息类型 - 对应 contact-us API
export interface ContactInfo {
  id: number;
  documentId?: string; // Strapi v5 新增字段
  address: string; // 地址信息
  email: string; // 邮箱地址
  lng: number; // 经度坐标
  lat: number; // 纬度坐标
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Strapi v5 API 响应格式 - 联系信息 (单一类型)
export interface StrapiContactResponse {
  data: ContactInfo;
  meta: {};
}