import api from './config';
import type { StrapiContactResponse, ContactInfo } from '../types';

/**
 * 联系信息 API 服务
 */
export class ContactService {
  /**
   * 获取联系信息
   * @returns 联系信息数据
   */
  static async getContactInfo(): Promise<ContactInfo> {
    try {
      // 添加调试日志
      const requestUrl = '/api/contact-us';
      console.log('Contact API 请求 URL:', requestUrl);

      const response = await api.get<StrapiContactResponse>(requestUrl);

      console.log('Contact API 响应:', response.data);

      // Strapi v5 单一类型 API 返回的是 { data: ContactInfo } 格式
      return response.data.data;
    } catch (error) {
      console.error('获取联系信息失败:', error);
      if (error.response) {
        console.error('错误响应状态:', error.response.status);
        console.error('错误响应数据:', error.response.data);
        console.error('错误响应头:', error.response.headers);

        // 如果是权限问题，返回示例数据用于演示
        if (error.response.status === 403) {
          console.warn('API权限未配置，使用示例数据');
          return {
            id: 1,
            address: 'GeoCUES Lab – Geographic Computation for Urban Economy & Society',
            email: '<EMAIL>',
            lng: 116.3074, // 北京大学附近坐标
            lat: 39.9925,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            publishedAt: new Date().toISOString()
          };
        }
      } else if (error.request) {
        console.error('请求错误:', error.request);
      } else {
        console.error('错误信息:', error.message);
      }
      throw error;
    }
  }
}

export default ContactService;
