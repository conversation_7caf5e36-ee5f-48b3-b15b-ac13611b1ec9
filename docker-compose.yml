version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: research_group_postgres
    environment:
      POSTGRES_DB: research_group_website
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - research_group_network
    restart: unless-stopped

volumes:
  postgres_data:
    name: research_group_postgres_data

networks:
  research_group_network:
    driver: bridge